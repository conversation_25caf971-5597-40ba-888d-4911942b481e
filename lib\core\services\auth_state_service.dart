import 'package:flutter/foundation.dart';
import '../di/service_locator.dart';
import '../../domain/repositories/auth_repository.dart';

enum AuthState {
  unknown,
  loading,
  authenticated,
  unauthenticated,
  tokenExpired,
}

class AuthStateService extends ChangeNotifier {
  AuthState _state = AuthState.unknown;
  String? _username;
  DateTime? _tokenExpiry;
  bool _isInitialized = false;

  AuthState get state => _state;
  String? get username => _username;
  DateTime? get tokenExpiry => _tokenExpiry;
  bool get isInitialized => _isInitialized;
  
  bool get isAuthenticated => _state == AuthState.authenticated;
  bool get isUnauthenticated => _state == AuthState.unauthenticated;
  bool get isLoading => _state == AuthState.loading;
  bool get isTokenExpired => _state == AuthState.tokenExpired;

  late AuthRepository _authRepository;

  /// Initialize the auth state service
  Future<void> init() async {
    if (_isInitialized) return;
    
    _authRepository = ServiceLocator().authRepository;
    await checkAuthState();
    _isInitialized = true;
  }

  /// Check current authentication state
  Future<void> checkAuthState() async {
    _setState(AuthState.loading);
    
    try {
      // Check if user has valid token
      final isLoggedIn = await _authRepository.isLoggedIn();
      
      if (isLoggedIn) {
        // Load user info
        await _loadUserInfo();
        _setState(AuthState.authenticated);
      } else {
        // Check if token exists but expired
        final token = await _authRepository.getStoredToken();
        if (token != null && token.isNotEmpty) {
          _setState(AuthState.tokenExpired);
        } else {
          _setState(AuthState.unauthenticated);
        }
      }
    } catch (e) {
      debugPrint('Auth state check error: $e');
      _setState(AuthState.unauthenticated);
    }
  }

  /// Load user information
  Future<void> _loadUserInfo() async {
    try {
      _username = await _authRepository.getStoredUsername();
      
      // Get token expiry if available
      // For now, we'll skip this as it requires extending the AuthRepository interface
    } catch (e) {
      debugPrint('Failed to load user info: $e');
    }
  }

  /// Handle successful login
  Future<void> onLoginSuccess(String? username) async {
    _username = username;
    await _loadUserInfo();
    _setState(AuthState.authenticated);
  }

  /// Handle logout
  Future<void> onLogout() async {
    try {
      await _authRepository.logout();
    } catch (e) {
      debugPrint('Logout error: $e');
    } finally {
      _username = null;
      _tokenExpiry = null;
      _setState(AuthState.unauthenticated);
    }
  }

  /// Handle token expiry
  void onTokenExpired() {
    _setState(AuthState.tokenExpired);
  }

  /// Refresh authentication state
  Future<void> refresh() async {
    await checkAuthState();
  }

  /// Set state and notify listeners
  void _setState(AuthState newState) {
    if (_state != newState) {
      _state = newState;
      notifyListeners();
    }
  }

  /// Get time until token expires
  Duration? get timeUntilExpiry {
    if (_tokenExpiry == null) return null;
    final now = DateTime.now();
    if (_tokenExpiry!.isBefore(now)) return Duration.zero;
    return _tokenExpiry!.difference(now);
  }

  /// Check if token will expire soon (within 5 minutes)
  bool get willExpireSoon {
    final timeLeft = timeUntilExpiry;
    if (timeLeft == null) return false;
    return timeLeft.inMinutes <= 5;
  }

  /// Get authentication status message
  String getStatusMessage() {
    switch (_state) {
      case AuthState.unknown:
        return 'Checking authentication...';
      case AuthState.loading:
        return 'Loading...';
      case AuthState.authenticated:
        return 'Authenticated';
      case AuthState.unauthenticated:
        return 'Not authenticated';
      case AuthState.tokenExpired:
        return 'Session expired';
    }
  }


}
