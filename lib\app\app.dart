import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import '../core/di/service_locator.dart';
import '../core/localization/localization_service.dart';
import '../generated/l10n/app_localizations.dart';
import 'routes.dart';
import 'themes.dart';

class UnisoftApp extends StatefulWidget {
  const UnisoftApp({super.key});

  @override
  State<UnisoftApp> createState() => _UnisoftAppState();
}

class _UnisoftAppState extends State<UnisoftApp> {
  late LocalizationService _localizationService;

  @override
  void initState() {
    super.initState();
    _localizationService = ServiceLocator().localizationService;
    _localizationService.addListener(_onLocaleChanged);
  }

  @override
  void dispose() {
    _localizationService.removeListener(_onLocaleChanged);
    super.dispose();
  }

  void _onLocaleChanged() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Unisoft Flutter Application',
      theme: AppThemes.lightTheme,
      darkTheme: AppThemes.darkTheme,
      themeMode: ThemeMode.system,
      locale: _localizationService.currentLocale,
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: AppLocalizations.supportedLocales,
      localeResolutionCallback: (locale, supportedLocales) {
        // If the device locale is supported, use it
        if (locale != null) {
          for (final supportedLocale in supportedLocales) {
            if (supportedLocale.languageCode == locale.languageCode) {
              return supportedLocale;
            }
          }
        }
        // Default to English if device locale is not supported
        return const Locale('en', 'US');
      },
      initialRoute: AppRoutes.splash,
      routes: AppRoutes.routes,
      debugShowCheckedModeBanner: false,
      onGenerateRoute: AppRoutes.onGenerateRoute,
    );
  }
}
