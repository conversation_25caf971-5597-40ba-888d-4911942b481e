import 'package:flutter/material.dart';
import '../../generated/l10n/app_localizations.dart';

/// Legacy AppStrings class for backward compatibility
///
/// This class provides static access to localized strings while maintaining
/// backward compatibility with existing code. It uses the new localization
/// system under the hood.
///
/// For new code, prefer using AppLocalizations.of(context) directly.
class AppStrings {
  static BuildContext? _context;

  /// Initialize the context for static string access
  /// This should be called from widgets that need to use AppStrings
  static void init(BuildContext context) {
    _context = context;
  }

  static AppLocalizations get _l10n {
    if (_context == null) {
      throw Exception(
        'AppStrings not initialized. Call AppStrings.init(context) first, '
        'or use AppLocalizations.of(context) directly.'
      );
    }
    return AppLocalizations.of(_context!);
  }

  // Common
  static String get ok => _l10n.ok;
  static String get cancel => _l10n.cancel;
  static String get save => _l10n.save;
  static String get delete => _l10n.delete;
  static String get edit => _l10n.edit;
  static String get add => _l10n.add;
  static String get search => _l10n.search;
  static String get loading => _l10n.loading;
  static String get retry => _l10n.retry;
  static String get error => _l10n.error;
  static String get success => _l10n.success;
  static String get warning => _l10n.warning;
  static String get info => _l10n.info;

  // Authentication
  static String get login => _l10n.login;
  static String get logout => _l10n.logout;
  static String get register => _l10n.register;
  static String get username => _l10n.username;
  static String get password => _l10n.password;
  static String get confirmPassword => _l10n.confirmPassword;
  static String get forgotPassword => _l10n.forgotPassword;
  static String get rememberPassword => _l10n.rememberPassword;
  static String get loginSuccess => _l10n.loginSuccess;
  static String get loginFailed => _l10n.loginFailed;

  // Validation Messages
  static String get fieldRequired => _l10n.fieldRequired;
  static String get invalidEmail => _l10n.invalidEmail;
  static String get passwordTooShort => _l10n.passwordTooShort;
  static String get passwordsNotMatch => _l10n.passwordsNotMatch;
  static String get usernameTooShort => _l10n.usernameTooShort;

  // Network Messages
  static String get noInternetConnection => _l10n.noInternetConnection;
  static String get serverError => _l10n.serverError;
  static String get requestTimeout => _l10n.requestTimeout;
  static String get unknownError => _l10n.unknownError;

  // Navigation
  static String get home => _l10n.home;
  static String get profile => _l10n.profile;
  static String get settings => _l10n.settings;
  static String get about => _l10n.about;

  // Additional localization methods
  static String get language => _l10n.language;
  static String get selectLanguage => _l10n.selectLanguage;
}
