import '../../data/models/login_request.dart';
import '../../data/models/login_response.dart';
import '../repositories/auth_repository.dart';

class LoginUseCase {
  final AuthRepository _authRepository;

  LoginUseCase(this._authRepository);

  Future<LoginResult> execute(LoginParams params) async {
    try {
      // Validate input
      final validationError = _validateInput(params);
      if (validationError != null) {
        return LoginResult.failure(validationError);
      }

      // Create login request
      final request = LoginRequest(
        username: params.username,
        password: params.password,
        rememberMe: params.rememberMe,
      );

      // Perform login
      final response = await _authRepository.login(request);

      if (response.success) {
        return LoginResult.success(
          token: response.token!,
          message: response.message ?? 'Đăng nhập thành công',
          userData: response.userData,
        );
      } else {
        return LoginResult.failure(
          response.error ?? 'Đăng nhập thất bại',
        );
      }
    } catch (e) {
      return LoginResult.failure('Lỗi không xác định: $e');
    }
  }

  String? _validateInput(LoginParams params) {
    if (params.username.isEmpty) {
      return 'Vui lòng nhập tên đăng nhập';
    }

    if (params.password.isEmpty) {
      return 'Vui lòng nhập mật khẩu';
    }

    if (params.username.length < 3) {
      return 'Tên đăng nhập phải có ít nhất 3 ký tự';
    }

    if (params.password.length < 6) {
      return 'Mật khẩu phải có ít nhất 6 ký tự';
    }

    return null;
  }
}

class LoginParams {
  final String username;
  final String password;
  final bool rememberMe;

  const LoginParams({
    required this.username,
    required this.password,
    required this.rememberMe,
  });

  @override
  String toString() {
    return 'LoginParams(username: $username, password: [HIDDEN], rememberMe: $rememberMe)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LoginParams &&
        other.username == username &&
        other.password == password &&
        other.rememberMe == rememberMe;
  }

  @override
  int get hashCode {
    return username.hashCode ^ password.hashCode ^ rememberMe.hashCode;
  }
}

class LoginResult {
  final bool isSuccess;
  final String? token;
  final String message;
  final UserData? userData;

  const LoginResult._({
    required this.isSuccess,
    this.token,
    required this.message,
    this.userData,
  });

  factory LoginResult.success({
    required String token,
    required String message,
    UserData? userData,
  }) {
    return LoginResult._(
      isSuccess: true,
      token: token,
      message: message,
      userData: userData,
    );
  }

  factory LoginResult.failure(String message) {
    return LoginResult._(
      isSuccess: false,
      message: message,
    );
  }

  @override
  String toString() {
    return 'LoginResult(isSuccess: $isSuccess, token: ${token != null ? '[HIDDEN]' : null}, message: $message, userData: $userData)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LoginResult &&
        other.isSuccess == isSuccess &&
        other.token == token &&
        other.message == message &&
        other.userData == userData;
  }

  @override
  int get hashCode {
    return isSuccess.hashCode ^
        token.hashCode ^
        message.hashCode ^
        userData.hashCode;
  }
}
