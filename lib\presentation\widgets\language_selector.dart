import 'package:flutter/material.dart';
import '../../core/di/service_locator.dart';
import '../../core/localization/localization_service.dart';
import '../../core/localization/locale_manager.dart';
import '../../generated/l10n/app_localizations.dart';

class LanguageSelector extends StatefulWidget {
  final bool showAsDialog;
  final VoidCallback? onLanguageChanged;

  const LanguageSelector({
    super.key,
    this.showAsDialog = false,
    this.onLanguageChanged,
  });

  @override
  State<LanguageSelector> createState() => _LanguageSelectorState();
}

class _LanguageSelectorState extends State<LanguageSelector> {
  late LocalizationService _localizationService;

  @override
  void initState() {
    super.initState();
    _localizationService = ServiceLocator().localizationService;
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    if (widget.showAsDialog) {
      return _buildDialog(context, l10n);
    } else {
      return _buildList(context, l10n);
    }
  }

  Widget _buildDialog(BuildContext context, AppLocalizations l10n) {
    return AlertDialog(
      title: Text(l10n.selectLanguage),
      content: SizedBox(
        width: double.minPositive,
        child: _buildList(context, l10n),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(l10n.cancel),
        ),
      ],
    );
  }

  Widget _buildList(BuildContext context, AppLocalizations l10n) {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: LocaleManager.supportedLocales.length,
      itemBuilder: (context, index) {
        final locale = LocaleManager.supportedLocales[index];
        final isSelected = _localizationService.isCurrentLocale(locale);
        
        return ListTile(
          title: Text(LocaleManager.getDisplayName(locale)),
          trailing: isSelected ? const Icon(Icons.check, color: Colors.green) : null,
          onTap: () async {
            if (!isSelected) {
              await _localizationService.changeLocale(locale);
              widget.onLanguageChanged?.call();
              
              if (widget.showAsDialog && mounted) {
                Navigator.of(context).pop();
              }
            }
          },
        );
      },
    );
  }
}

class LanguageSelectorTile extends StatelessWidget {
  final VoidCallback? onTap;

  const LanguageSelectorTile({
    super.key,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final localizationService = ServiceLocator().localizationService;

    return ListTile(
      leading: const Icon(Icons.language),
      title: Text(l10n.language),
      subtitle: Text(localizationService.currentLocaleDisplayName),
      trailing: const Icon(Icons.arrow_forward_ios),
      onTap: onTap ?? () => _showLanguageDialog(context),
    );
  }

  void _showLanguageDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const LanguageSelector(showAsDialog: true),
    );
  }
}

class LanguageDropdown extends StatefulWidget {
  final ValueChanged<Locale>? onChanged;

  const LanguageDropdown({
    super.key,
    this.onChanged,
  });

  @override
  State<LanguageDropdown> createState() => _LanguageDropdownState();
}

class _LanguageDropdownState extends State<LanguageDropdown> {
  late LocalizationService _localizationService;

  @override
  void initState() {
    super.initState();
    _localizationService = ServiceLocator().localizationService;
  }

  @override
  Widget build(BuildContext context) {
    return DropdownButton<Locale>(
      value: _localizationService.currentLocale,
      items: LocaleManager.supportedLocales.map((locale) {
        return DropdownMenuItem<Locale>(
          value: locale,
          child: Text(LocaleManager.getDisplayName(locale)),
        );
      }).toList(),
      onChanged: (locale) async {
        if (locale != null && locale != _localizationService.currentLocale) {
          await _localizationService.changeLocale(locale);
          widget.onChanged?.call(locale);
        }
      },
    );
  }
}
