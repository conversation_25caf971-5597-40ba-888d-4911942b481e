class LoginRequest {
  final String username;
  final String password;
  final bool rememberMe;

  const LoginRequest({
    required this.username,
    required this.password,
    required this.rememberMe,
  });

  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'password': password,
      'rememberMe': rememberMe,
    };
  }

  factory LoginRequest.fromJson(Map<String, dynamic> json) {
    return LoginRequest(
      username: json['username'] as String,
      password: json['password'] as String,
      rememberMe: json['rememberMe'] as bool,
    );
  }

  @override
  String toString() {
    return 'LoginRequest(username: $username, password: [HIDDEN], rememberMe: $rememberMe)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LoginRequest &&
        other.username == username &&
        other.password == password &&
        other.rememberMe == rememberMe;
  }

  @override
  int get hashCode {
    return username.hashCode ^ password.hashCode ^ rememberMe.hashCode;
  }

  LoginRequest copyWith({
    String? username,
    String? password,
    bool? rememberMe,
  }) {
    return LoginRequest(
      username: username ?? this.username,
      password: password ?? this.password,
      rememberMe: rememberMe ?? this.rememberMe,
    );
  }
}
