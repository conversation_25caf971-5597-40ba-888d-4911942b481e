import '../../data/models/login_request.dart';
import '../../data/models/login_response.dart';

abstract class AuthRepository {
  Future<LoginResponse> login(LoginRequest request);
  Future<void> logout();
  Future<bool> isLoggedIn();
  Future<String?> getStoredToken();
  Future<void> saveToken(String token);
  Future<void> clearToken();
  Future<bool> getRememberMe();
  Future<void> saveRememberMe(bool rememberMe);
  Future<String?> getStoredUsername();
  Future<void> saveUsername(String username);
  Future<void> clearStoredCredentials();
}
