import 'package:shared_preferences/shared_preferences.dart';
import '../../data/datasources/auth_api_service.dart';
import '../../data/repositories/auth_repository_impl.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../domain/usecases/login_usecase.dart';
import '../localization/localization_service.dart';
import '../network/auth_interceptor.dart';
import '../network/http_client_interface.dart';
import '../security/token_manager.dart';
import '../services/auth_state_service.dart';

class ServiceLocator {
  static final ServiceLocator _instance = ServiceLocator._internal();
  factory ServiceLocator() => _instance;
  ServiceLocator._internal();

  // Dependencies
  late SharedPreferences _prefs;
  late TokenManager _tokenManager;
  late HttpClientInterface _apiClient;
  late AuthApiService _authApiService;
  late AuthRepository _authRepository;
  late LoginUseCase _loginUseCase;
  late LocalizationService _localizationService;
  late AuthStateService _authStateService;

  bool _isInitialized = false;

  // Initialize all dependencies
  Future<void> init() async {
    if (_isInitialized) return;

    // Initialize SharedPreferences
    _prefs = await SharedPreferences.getInstance();

    // Initialize TokenManager
    _tokenManager = TokenManager();

    // Initialize API client with authentication
    _apiClient = AuthenticatedApiClient(tokenManager: _tokenManager);

    // Initialize API services
    _authApiService = AuthApiServiceImpl(apiClient: _apiClient);

    // Initialize repositories
    _authRepository = AuthRepositoryImpl(
      apiService: _authApiService,
      prefs: _prefs,
      tokenManager: _tokenManager,
    );

    // Initialize use cases
    _loginUseCase = LoginUseCase(_authRepository);

    // Initialize localization service
    _localizationService = LocalizationService();
    await _localizationService.init();

    // Initialize auth state service
    _authStateService = AuthStateService();
    await _authStateService.init();

    _isInitialized = true;
  }

  // Getters for dependencies
  SharedPreferences get prefs {
    _checkInitialized();
    return _prefs;
  }

  HttpClientInterface get apiClient {
    _checkInitialized();
    return _apiClient;
  }

  AuthApiService get authApiService {
    _checkInitialized();
    return _authApiService;
  }

  AuthRepository get authRepository {
    _checkInitialized();
    return _authRepository;
  }

  LoginUseCase get loginUseCase {
    _checkInitialized();
    return _loginUseCase;
  }

  LocalizationService get localizationService {
    _checkInitialized();
    return _localizationService;
  }

  AuthStateService get authStateService {
    _checkInitialized();
    return _authStateService;
  }

  void _checkInitialized() {
    if (!_isInitialized) {
      throw Exception('ServiceLocator not initialized. Call init() first.');
    }
  }

  // Dispose resources
  void dispose() {
    if (_isInitialized) {
      _apiClient.dispose();
      if (_authApiService is AuthApiServiceImpl) {
        (_authApiService as AuthApiServiceImpl).dispose();
      }
      _isInitialized = false;
    }
  }

  // Reset for testing
  void reset() {
    dispose();
    _isInitialized = false;
  }
}
