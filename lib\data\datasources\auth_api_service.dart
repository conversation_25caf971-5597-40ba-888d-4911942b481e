import 'dart:convert';
import '../../core/network/http_client_interface.dart';
import '../models/login_request.dart';
import '../models/login_response.dart';

abstract class AuthApiService {
  Future<LoginResponse> login(LoginRequest request);
  Future<void> logout();
  Future<bool> validateToken(String token);
}

class AuthApiServiceImpl implements AuthApiService {
  final HttpClientInterface _apiClient;

  AuthApiServiceImpl({required HttpClientInterface apiClient})
      : _apiClient = apiClient;

  @override
  Future<LoginResponse> login(LoginRequest request) async {
    try {
      final response = await _apiClient.post(
        '/auth/v1/authentication/jwt/login',
        body: request.toJson(),
      );

      final responseData = jsonDecode(response.body);

      // Parse response using the new format
      return LoginResponse.fromJson(responseData);
    } on ApiException catch (e) {
      return LoginResponse(
        success: false,
        error: e.message,
      );
    } on NetworkException catch (e) {
      return LoginResponse(
        success: false,
        error: e.message,
      );
    } catch (e) {
      return LoginResponse(
        success: false,
        error: 'Lỗi không xác định: $e',
      );
    }
  }

  @override
  Future<void> logout() async {
    try {
      // Implement logout API call if available
      await _apiClient.post('/auth/v1/authentication/logout');
    } catch (e) {
      // Log error but don't throw - logout should always succeed locally
      print('Logout API error: $e');
    }
  }

  @override
  Future<bool> validateToken(String token) async {
    try {
      // First check JWT format and expiry locally
      if (!_isValidJWTFormat(token)) {
        return false;
      }

      final expiry = _getJWTExpiry(token);
      if (expiry != null && DateTime.now().isAfter(expiry)) {
        return false;
      }

      // Then validate with server (optional - comment out if no validation endpoint)
      /*
      final response = await _apiClient.get(
        '/auth/v1/authentication/validate',
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      return response.statusCode == 200;
      */

      return true; // Token is valid locally
    } catch (e) {
      return false;
    }
  }

  // Check if token has valid JWT format
  bool _isValidJWTFormat(String token) {
    final parts = token.split('.');
    return parts.length == 3;
  }

  // Extract expiry from JWT token
  DateTime? _getJWTExpiry(String token) {
    try {
      if (!_isValidJWTFormat(token)) return null;

      final parts = token.split('.');
      final payload = parts[1];

      // Add padding if needed
      String normalizedPayload = payload;
      while (normalizedPayload.length % 4 != 0) {
        normalizedPayload += '=';
      }

      final decoded = base64.decode(normalizedPayload);
      final jsonString = utf8.decode(decoded);
      final payloadMap = jsonDecode(jsonString) as Map<String, dynamic>;

      final exp = payloadMap['exp'] as int?;
      if (exp == null) return null;

      return DateTime.fromMillisecondsSinceEpoch(exp * 1000);
    } catch (e) {
      return null;
    }
  }



  void dispose() {
    _apiClient.dispose();
  }
}
