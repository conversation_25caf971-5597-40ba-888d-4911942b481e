# Project Documentation Index

This folder contains comprehensive documentation for the Unisoft Flutter Application.

## 📚 Documentation Files

### 1. **[Project Structure](project_structure.md)**
- Detailed project architecture and folder organization
- Clean Architecture implementation
- File and folder descriptions
- Best practices and conventions

### 2. **[API Integration](API_INTEGRATION.md)**
- API endpoint documentation
- Request/Response formats
- Authentication flow
- Error handling
- Testing guidelines

### 3. **[Token Security](TOKEN_SECURITY.md)**
- Security implementation details
- Token encryption and storage
- Multi-platform secure storage
- JWT token management
- HTTP interceptor system

### 4. **[Original README](README.md)**
- Original project README file
- Basic project information

## 🏗️ Architecture Overview

The project follows **Clean Architecture** principles with clear separation of concerns:

```
┌─────────────────┐
│  Presentation   │ ← UI Layer (Screens, Widgets)
├─────────────────┤
│    Domain       │ ← Business Logic (Use Cases, Entities)
├─────────────────┤
│     Data        │ ← Data Layer (Repositories, API, Storage)
├─────────────────┤
│     Core        │ ← Shared Utilities (Network, Security, Utils)
└─────────────────┘
```

## 🔐 Security Features

- **Multi-layer Token Encryption**
- **Platform-specific Secure Storage**
- **JWT Token Management**
- **HTTP Request Interceptor**
- **Automatic Token Validation**
- **Session Management**

## 🚀 Quick Navigation

| Topic | File | Description |
|-------|------|-------------|
| **Architecture** | [project_structure.md](project_structure.md) | Project organization and structure |
| **API** | [API_INTEGRATION.md](API_INTEGRATION.md) | API integration and usage |
| **Security** | [TOKEN_SECURITY.md](TOKEN_SECURITY.md) | Token security implementation |
| **Original** | [README.md](README.md) | Original project README |

## 📋 Development Guidelines

### Code Organization
- Follow the established folder structure
- Use meaningful names for files and classes
- Implement proper error handling
- Add comprehensive comments

### Security Best Practices
- Never hardcode sensitive information
- Use secure storage for tokens
- Implement proper validation
- Handle errors gracefully

### Testing
- Write unit tests for business logic
- Test API integrations
- Validate security implementations
- Test across different platforms

## 🔧 Configuration

### Environment Setup
1. Flutter SDK (>=3.8.1)
2. Required dependencies installed
3. Platform-specific setup completed

### API Configuration
- Base URL: `http://123.30.240.49:30200`
- Authentication endpoint: `/auth/v1/authentication/jwt/login`
- Content-Type: `application/json`

## 📞 Support

For questions or issues:
1. Check the relevant documentation file
2. Review the code comments
3. Test with provided examples
4. Contact the development team

---

**Documentation Version**: 1.0.0  
**Last Updated**: 2025-07-20  
**Project**: Unisoft Flutter Application
