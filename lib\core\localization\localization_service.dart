import 'package:flutter/material.dart';
import 'locale_manager.dart';

class LocalizationService extends ChangeNotifier {
  Locale? _currentLocale;

  Locale? get currentLocale => _currentLocale;

  /// Initialize the localization service
  Future<void> init() async {
    await LocaleManager.init();
    _currentLocale = LocaleManager.currentLocale;
  }

  /// Change the current locale
  Future<void> changeLocale(Locale locale) async {
    if (_currentLocale == locale) return;

    await LocaleManager.setLocale(locale);
    _currentLocale = locale;
    notifyListeners();
  }

  /// Get supported locales
  List<Locale> get supportedLocales => LocaleManager.supportedLocales;

  /// Get display name for current locale
  String get currentLocaleDisplayName {
    if (_currentLocale == null) return 'English';
    return LocaleManager.getDisplayName(_currentLocale!);
  }

  /// Get display name for a specific locale
  String getDisplayName(Locale locale) {
    return LocaleManager.getDisplayName(locale);
  }

  /// Check if a locale is the current locale
  bool isCurrentLocale(Locale locale) {
    return _currentLocale?.languageCode == locale.languageCode;
  }
}
