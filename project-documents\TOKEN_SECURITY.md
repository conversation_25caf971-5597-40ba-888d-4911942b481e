# Token Security & Storage - Unisoft Flutter Application

## Tổng Quan

Ứng dụng đã được thiết lập với cơ chế bảo mật và lưu trữ token tiên tiến, đả<PERSON> bảo an toàn tối đa cho thông tin xác thực người dùng.

## API Response Format

### Login Response Structure
```json
{
    "data": {
        "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expiresIn": 3600,
        "userInfo": {
            "id": 151,
            "isActive": true,
            "userName": "admin",
            "fullName": "Phòng đào tạo",
            "maCanBo": "admin"
        }
    },
    "code": 200,
    "message": "Thành công",
    "requestDuration": 57,
    "traceId": "d47d86b9-a3e1-4535-b15d-7ecdbed771e1"
}
```

## C<PERSON> Chế Bảo Mật Token

### 1. **Secure Storage với Flutter Secure Storage**

#### Platform-Specific Security:
- **Android**: Sử dụng EncryptedSharedPreferences với AES encryption
- **iOS**: Sử dụng Keychain Services với hardware-backed security
- **Windows**: Sử dụng Windows Credential Store
- **Linux**: Sử dụng Secret Service API
- **macOS**: Sử dụng Keychain Services

#### Configuration:
```dart
FlutterSecureStorage(
  aOptions: AndroidOptions(
    encryptedSharedPreferences: true,
    sharedPreferencesName: 'unisoft_secure_prefs',
    preferencesKeyPrefix: 'unisoft_',
  ),
  iOptions: IOSOptions(
    groupId: 'group.com.unisoft.app',
    accountName: 'unisoft_account',
    accessibility: IOSAccessibility.first_unlock_this_device,
  ),
)
```

### 2. **Token Encryption Layer**

#### Additional Encryption:
- **XOR Encryption**: Thêm lớp mã hóa XOR với key ngẫu nhiên
- **Base64 Encoding**: Encode dữ liệu đã mã hóa
- **Random Key Generation**: Tạo key mã hóa ngẫu nhiên cho mỗi device

#### Process:
1. Generate random encryption key (32 characters)
2. XOR encrypt token với key
3. Base64 encode encrypted data
4. Store encrypted token trong secure storage
5. Store encryption key riêng biệt

### 3. **Token Management Features**

#### Automatic Expiry Handling:
- **JWT Parsing**: Tự động parse expiry time từ JWT token
- **Buffer Time**: Thêm 5 phút buffer trước khi token hết hạn
- **Auto Validation**: Kiểm tra token validity trước mỗi request
- **Auto Cleanup**: Tự động xóa expired tokens

#### Token Validation:
```dart
// Local validation
bool isValidJWTFormat(String token);
DateTime? getJWTExpiry(String token);
bool isTokenExpired();

// Server validation (optional)
Future<bool> validateToken(String token);
```

### 4. **HTTP Interceptor với Auto-Authentication**

#### Features:
- **Automatic Token Attachment**: Tự động thêm Bearer token vào headers
- **401 Handling**: Tự động xử lý unauthorized responses
- **Token Refresh**: Cơ chế refresh token (sẵn sàng cho tương lai)
- **Request Retry**: Retry requests sau khi refresh token

#### Implementation:
```dart
class AuthInterceptor extends http.BaseClient {
  // Automatically adds Authorization header
  Future<void> _addAuthHeader(http.BaseRequest request);
  
  // Handles 401 responses
  Future<void> _handleUnauthorized();
}
```

## Cấu Trúc Components

### 1. **TokenManager** (`lib/core/security/token_manager.dart`)
- **Secure Storage**: Quản lý lưu trữ an toàn
- **Encryption/Decryption**: Mã hóa/giải mã token
- **Expiry Management**: Quản lý thời gian hết hạn
- **JWT Parsing**: Parse và validate JWT tokens

### 2. **AuthInterceptor** (`lib/core/network/auth_interceptor.dart`)
- **HTTP Interceptor**: Tự động attach token
- **Error Handling**: Xử lý 401 responses
- **Token Refresh**: Cơ chế refresh (future)

### 3. **AuthenticatedApiClient** (`lib/core/network/auth_interceptor.dart`)
- **Enhanced HTTP Client**: Client với authentication tự động
- **Request/Response Handling**: Xử lý requests với token
- **Error Management**: Quản lý lỗi authentication

### 4. **AuthRepository** (`lib/data/repositories/auth_repository_impl.dart`)
- **Token Persistence**: Lưu trữ token sau login
- **Session Management**: Quản lý session người dùng
- **Logout Handling**: Xóa tokens khi logout

## Security Best Practices

### 1. **Token Storage**
✅ **Secure**: Sử dụng platform-specific secure storage
✅ **Encrypted**: Thêm lớp encryption tùy chỉnh
✅ **Isolated**: Tách biệt token và encryption key
✅ **Auto-cleanup**: Tự động xóa expired tokens

### 2. **Token Transmission**
✅ **HTTPS Only**: Chỉ truyền token qua HTTPS
✅ **Bearer Format**: Sử dụng standard Bearer token format
✅ **Auto-attachment**: Tự động attach vào requests
✅ **Error Handling**: Xử lý lỗi authentication

### 3. **Token Validation**
✅ **Local Validation**: Kiểm tra format và expiry locally
✅ **Server Validation**: Optional server-side validation
✅ **Expiry Buffer**: 5-minute buffer trước expiry
✅ **Auto-refresh**: Sẵn sàng cho token refresh

### 4. **Session Management**
✅ **Remember Me**: Lưu credentials khi được chọn
✅ **Auto-login**: Tự động đăng nhập với valid token
✅ **Secure Logout**: Xóa tất cả tokens khi logout
✅ **Session Timeout**: Tự động logout khi token expired

## API Integration

### 1. **Login Flow**
```dart
1. User enters credentials
2. Send login request to API
3. Receive response with accessToken & expiresIn
4. Encrypt and store token securely
5. Save user preferences (remember me)
6. Navigate to home screen
```

### 2. **Authenticated Requests**
```dart
1. Check if token exists and valid
2. Automatically attach Bearer token to headers
3. Send request to API
4. Handle 401 responses (token expired)
5. Auto-cleanup expired tokens
```

### 3. **Logout Flow**
```dart
1. Call logout API (optional)
2. Clear all stored tokens
3. Clear user session data
4. Navigate to login screen
```

## Configuration

### Token Storage Keys
```dart
static const String _tokenKey = 'secure_access_token';
static const String _tokenExpiryKey = 'token_expiry';
static const String _refreshTokenKey = 'secure_refresh_token';
static const String _encryptionKeyKey = 'encryption_key';
```

### Security Settings
```dart
// Token validation buffer
const Duration bufferTime = Duration(minutes: 5);

// HTTP timeouts
const Duration connectionTimeout = Duration(seconds: 30);
const Duration receiveTimeout = Duration(seconds: 30);
```

## Testing & Debugging

### 1. **Token Validation Testing**
```dart
// Test token format
bool isValid = tokenManager.isValidJWTFormat(token);

// Test token expiry
DateTime? expiry = tokenManager.getJWTExpiry(token);

// Test encryption/decryption
String encrypted = await tokenManager._encryptData(data);
String decrypted = await tokenManager._decryptData(encrypted);
```

### 2. **Security Testing**
- Test token storage across app restarts
- Test token encryption/decryption
- Test automatic token cleanup
- Test 401 response handling

### 3. **Integration Testing**
- Test login flow with real API
- Test authenticated requests
- Test logout flow
- Test remember me functionality

## Future Enhancements

### Planned Security Features
- [ ] **Biometric Authentication**: Fingerprint/Face ID
- [ ] **Token Refresh**: Automatic token refresh
- [ ] **Certificate Pinning**: SSL certificate pinning
- [ ] **Root Detection**: Detect rooted/jailbroken devices
- [ ] **App Integrity**: Verify app signature
- [ ] **Network Security**: Additional network security layers

### Advanced Features
- [ ] **Multi-factor Authentication**: 2FA support
- [ ] **Device Registration**: Register trusted devices
- [ ] **Session Analytics**: Track session security
- [ ] **Threat Detection**: Detect suspicious activities

## Troubleshooting

### Common Issues

1. **Token Storage Fails**
   - Check device security settings
   - Verify app permissions
   - Clear app data and retry

2. **Token Decryption Fails**
   - Token may be corrupted
   - Encryption key may be lost
   - Clear tokens and re-login

3. **Authentication Fails**
   - Check token expiry
   - Verify API endpoint
   - Check network connectivity

### Debug Commands
```dart
// Check token status
bool hasToken = await tokenManager.hasValidToken();

// Get token info
String? token = await tokenManager.getAccessToken();
DateTime? expiry = await tokenManager.getTokenExpiry();

// Clear all tokens
await tokenManager.clearAllTokens();
```

---

*Cập nhật lần cuối: 2025-07-20*
