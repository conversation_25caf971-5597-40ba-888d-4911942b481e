# Cấu Trúc Project - Unisoft Flutter Application

## Tổng Quan

Dự án Flutter này được tổ chức theo kiến trúc Clean Architecture với cấu trúc thư mục rõ ràng, dễ bảo trì và mở rộng. Cấu trúc này giúp tách biệt các tầng logic, UI và dữ liệu một cách hiệu quả.

## Cấu Trúc Thư Mục

```
unisoft_flutter_application/
├── android/                    # Cấu hình và code native Android
├── ios/                        # Cấu hình và code native iOS  
├── linux/                      # Cấu hình cho Linux desktop
├── macos/                      # Cấu hình cho macOS desktop
├── windows/                    # Cấu hình cho Windows desktop
├── web/                        # Cấu hình cho web platform
├── lib/                        # Code Dart chính của ứng dụng
│   ├── main.dart              # Entry point của ứng dụng
│   ├── app/                   # Cấu hình ứng dụng
│   │   ├── app.dart          # Main app widget
│   │   ├── routes.dart       # Định nghĩa routes và navigation
│   │   └── themes.dart       # Themes và styling configuration
│   ├── core/                  # Core functionality và utilities
│   │   ├── constants/        # Constants, enums, app-wide values
│   │   │   ├── app_constants.dart
│   │   │   └── app_strings.dart
│   │   ├── utils/            # Utility functions và helpers
│   │   │   ├── validators.dart
│   │   │   └── helpers.dart
│   │   └── extensions/       # Dart extensions
│   │       ├── string_extensions.dart
│   │       └── context_extensions.dart
│   ├── data/                  # Data layer - External data sources
│   │   ├── datasources/      # API calls, local storage implementations
│   │   ├── models/           # Data models (JSON serialization)
│   │   ├── repositories/     # Repository implementations
│   │   └── services/         # External services (Firebase, etc.)
│   ├── domain/                # Business logic layer
│   │   ├── entities/         # Business entities (pure Dart objects)
│   │   ├── repositories/     # Repository interfaces/contracts
│   │   └── usecases/         # Business use cases và logic
│   ├── presentation/          # UI layer - Everything user sees
│   │   ├── screens/          # Screen widgets (pages)
│   │   │   └── login_screen.dart
│   │   ├── widgets/          # Reusable UI components
│   │   └── providers/        # State management (Provider, Riverpod, BLoC)
│   └── shared/                # Shared components across the app
│       ├── widgets/          # Common UI components
│       └── styles/           # Shared styles, colors, dimensions
├── pubspec.yaml               # Dependencies và project configuration
├── pubspec.lock               # Lock file cho dependencies
├── analysis_options.yaml     # Dart/Flutter linting rules
├── README.md                  # Tài liệu dự án
└── project_structure.md       # File này - Mô tả cấu trúc project
```

## Mô Tả Chi Tiết Các Thư Mục

### 1. **lib/main.dart**
- Entry point chính của ứng dụng
- Khởi tạo và chạy ứng dụng Flutter
- Import và sử dụng UnisoftApp từ app/app.dart

### 2. **lib/app/** - Cấu hình ứng dụng
- **app.dart**: Widget chính của ứng dụng, cấu hình MaterialApp
- **routes.dart**: Định nghĩa tất cả routes, navigation helpers
- **themes.dart**: Light/Dark themes, colors, text styles

### 3. **lib/core/** - Chức năng cốt lõi
- **constants/**: 
  - `app_constants.dart`: API URLs, timeouts, validation rules
  - `app_strings.dart`: Tất cả text strings sử dụng trong app
- **utils/**:
  - `validators.dart`: Form validation functions
  - `helpers.dart`: Utility functions (snackbars, dialogs, formatting)
- **extensions/**:
  - `string_extensions.dart`: String manipulation extensions
  - `context_extensions.dart`: BuildContext convenience methods

### 4. **lib/data/** - Tầng dữ liệu
- **datasources/**: Implementations cho data sources
  - Remote data sources (API calls)
  - Local data sources (SharedPreferences, SQLite)
- **models/**: Data models với JSON serialization
- **repositories/**: Concrete implementations của repository interfaces
- **services/**: External service integrations (Firebase, Analytics)

### 5. **lib/domain/** - Tầng business logic
- **entities/**: Pure Dart objects representing business concepts
- **repositories/**: Abstract interfaces cho data access
- **usecases/**: Business logic và use cases

### 6. **lib/presentation/** - Tầng giao diện
- **screens/**: Các màn hình chính của ứng dụng
  - `login_screen.dart`: Màn hình đăng nhập với form validation
- **widgets/**: Reusable UI components
- **providers/**: State management solutions

### 7. **lib/shared/** - Components dùng chung
- **widgets/**: Common UI components sử dụng across multiple screens
- **styles/**: Shared styling constants, colors, dimensions

## Nguyên Tắc Thiết Kế

### 1. **Separation of Concerns**
- Mỗi layer có trách nhiệm riêng biệt
- UI logic tách biệt khỏi business logic
- Data access tách biệt khỏi business rules

### 2. **Dependency Inversion**
- High-level modules không phụ thuộc vào low-level modules
- Cả hai đều phụ thuộc vào abstractions (interfaces)

### 3. **Single Responsibility**
- Mỗi class/file có một trách nhiệm duy nhất
- Dễ test và maintain

### 4. **Reusability**
- Components được thiết kế để tái sử dụng
- Shared utilities và extensions

## Lợi Ích Của Cấu Trúc Này

### 1. **Scalability** - Khả năng mở rộng
- Dễ dàng thêm features mới
- Cấu trúc rõ ràng cho team lớn

### 2. **Maintainability** - Dễ bảo trì
- Code được tổ chức logic
- Dễ tìm và sửa bugs

### 3. **Testability** - Dễ test
- Logic tách biệt rõ ràng
- Dễ viết unit tests và integration tests

### 4. **Team Collaboration** - Hợp tác nhóm
- Nhiều developer có thể làm việc song song
- Ít conflict khi merge code

## Hướng Dẫn Sử Dụng

### Thêm Screen Mới
1. Tạo file trong `lib/presentation/screens/`
2. Thêm route trong `lib/app/routes.dart`
3. Import và sử dụng

### Thêm Utility Function
1. Thêm vào `lib/core/utils/helpers.dart`
2. Hoặc tạo file mới trong `lib/core/utils/`

### Thêm Constants
1. Thêm vào `lib/core/constants/app_constants.dart`
2. Hoặc `lib/core/constants/app_strings.dart` cho text

### Thêm Extension
1. Tạo file trong `lib/core/extensions/`
2. Import khi cần sử dụng

## Best Practices

1. **Import Organization**: Sắp xếp imports theo thứ tự: Dart core, Flutter, Third-party, Local
2. **Naming Conventions**: Sử dụng snake_case cho files, PascalCase cho classes
3. **Documentation**: Comment cho complex logic
4. **Error Handling**: Implement proper error handling ở mọi layer
5. **State Management**: Sử dụng consistent state management solution

## Công Nghệ Sử Dụng

- **Flutter**: UI framework
- **Dart**: Programming language
- **Material Design 3**: Design system
- **Clean Architecture**: Architectural pattern

## Tác Giả

Dự án được tổ chức và phát triển theo best practices của Flutter community.

---

*Cập nhật lần cuối: 2025-07-20*
