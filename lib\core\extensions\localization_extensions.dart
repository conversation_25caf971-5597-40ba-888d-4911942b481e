import 'package:flutter/material.dart';
import '../../generated/l10n/app_localizations.dart';

/// Extension to make accessing localized strings easier
extension LocalizationExtension on BuildContext {
  /// Get the AppLocalizations instance for this context
  AppLocalizations get l10n => AppLocalizations.of(this);
  
  /// Get the current locale
  Locale get locale => Localizations.localeOf(this);
  
  /// Check if the current locale is RTL (Right-to-Left)
  bool get isRTL => Directionality.of(this) == TextDirection.rtl;
  
  /// Get the language code of the current locale
  String get languageCode => locale.languageCode;
  
  /// Check if the current language is Vietnamese
  bool get isVietnamese => languageCode == 'vi';
  
  /// Check if the current language is English
  bool get isEnglish => languageCode == 'en';
}

/// Extension to add localization helpers to String
extension StringLocalizationExtension on String {
  /// Capitalize the first letter of the string
  String get capitalize {
    if (isEmpty) return this;
    return '${this[0].toUpperCase()}${substring(1)}';
  }
  
  /// Convert to title case (capitalize each word)
  String get titleCase {
    if (isEmpty) return this;
    return split(' ').map((word) => word.capitalize).join(' ');
  }
}
