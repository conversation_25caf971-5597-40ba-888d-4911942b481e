# Internationalization (i18n) and Localization Guide

This document provides a comprehensive guide on how to use the internationalization and localization features in the Unisoft Flutter Application.

## Overview

The app supports multiple languages with the following features:
- **Supported Languages**: Vietnamese (vi) and English (en)
- **Dynamic Language Switching**: Users can change languages at runtime
- **Persistent Language Selection**: The selected language is saved and restored
- **Device Locale Detection**: Automatically uses device language if supported
- **Backward Compatibility**: Existing code using `AppStrings` continues to work

## Architecture

### Core Components

1. **LocaleManager** (`lib/core/localization/locale_manager.dart`)
   - Manages supported locales
   - Handles locale persistence with SharedPreferences
   - Provides utility methods for locale operations

2. **LocalizationService** (`lib/core/localization/localization_service.dart`)
   - ChangeNotifier for reactive locale changes
   - Integrated with dependency injection system
   - Provides high-level locale management API

3. **Generated Localization Classes** (`lib/generated/l10n/`)
   - Auto-generated from ARB files
   - Type-safe access to localized strings
   - Supports pluralization and parameterization

4. **ARB Files** (`lib/l10n/`)
   - `app_en.arb`: English translations (template)
   - `app_vi.arb`: Vietnamese translations

## Usage Examples

### 1. Basic Usage in Widgets

```dart
import 'package:flutter/material.dart';
import '../generated/l10n/app_localizations.dart';

class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.settings),
      ),
      body: Column(
        children: [
          Text(l10n.welcome),
          ElevatedButton(
            onPressed: () {},
            child: Text(l10n.save),
          ),
        ],
      ),
    );
  }
}
```

### 2. Using the Extension (Recommended)

```dart
import '../core/extensions/localization_extensions.dart';

class MyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.l10n.settings),
      ),
      body: Text(context.l10n.welcome),
    );
  }
}
```

### 3. Backward Compatibility with AppStrings

```dart
import '../core/constants/app_strings.dart';

class LegacyWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // Initialize AppStrings for this context
    AppStrings.init(context);
    
    return Text(AppStrings.login); // Works with new localization system
  }
}
```

### 4. Language Switching

```dart
import '../core/di/service_locator.dart';
import '../presentation/widgets/language_selector.dart';

// Using the pre-built language selector
class SettingsPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ListView(
      children: [
        LanguageSelectorTile(), // Shows current language and opens selector
      ],
    );
  }
}

// Manual language switching
class CustomLanguageSwitch extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final localizationService = ServiceLocator().localizationService;
    
    return ElevatedButton(
      onPressed: () async {
        final newLocale = Locale('vi', 'VN');
        await localizationService.changeLocale(newLocale);
      },
      child: Text('Switch to Vietnamese'),
    );
  }
}
```

## Adding New Translations

### 1. Add to ARB Files

**English (`lib/l10n/app_en.arb`):**
```json
{
  "welcomeMessage": "Welcome to our app!",
  "@welcomeMessage": {
    "description": "Welcome message shown on home screen"
  }
}
```

**Vietnamese (`lib/l10n/app_vi.arb`):**
```json
{
  "welcomeMessage": "Chào mừng bạn đến với ứng dụng của chúng tôi!"
}
```

### 2. Regenerate Localization Classes

```bash
flutter gen-l10n
```

### 3. Use in Code

```dart
Text(context.l10n.welcomeMessage)
```

## Parameterized Translations

### ARB File Example

```json
{
  "welcomeUser": "Welcome, {userName}!",
  "@welcomeUser": {
    "description": "Welcome message with user name",
    "placeholders": {
      "userName": {
        "type": "String",
        "example": "John"
      }
    }
  }
}
```

### Usage

```dart
Text(context.l10n.welcomeUser('John'))
```

## Pluralization

### ARB File Example

```json
{
  "itemCount": "{count, plural, =0{No items} =1{One item} other{{count} items}}",
  "@itemCount": {
    "description": "Number of items",
    "placeholders": {
      "count": {
        "type": "int"
      }
    }
  }
}
```

### Usage

```dart
Text(context.l10n.itemCount(5)) // "5 items"
```

## Best Practices

1. **Use Extensions**: Prefer `context.l10n.stringKey` over `AppLocalizations.of(context).stringKey`

2. **Initialize AppStrings**: If using legacy `AppStrings`, always call `AppStrings.init(context)` first

3. **Descriptive Keys**: Use descriptive keys like `loginButton` instead of `login`

4. **Add Descriptions**: Always add `@key` descriptions in ARB files for translators

5. **Test Both Languages**: Always test your UI with both supported languages

6. **Handle Long Text**: Consider text length differences between languages in your UI design

## Configuration Files

### `l10n.yaml`
```yaml
arb-dir: lib/l10n
template-arb-file: app_en.arb
output-localization-file: app_localizations.dart
output-class: AppLocalizations
output-dir: lib/generated/l10n
nullable-getter: false
synthetic-package: false
```

### `pubspec.yaml` Dependencies
```yaml
dependencies:
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2

flutter:
  generate: true
```

## Troubleshooting

### Common Issues

1. **"AppLocalizations.of() called with a context that does not contain a Localizations widget"**
   - Ensure your app has `localizationsDelegates` and `supportedLocales` configured

2. **"AppStrings not initialized"**
   - Call `AppStrings.init(context)` before using AppStrings static methods

3. **Missing translations**
   - Run `flutter gen-l10n` after adding new strings to ARB files
   - Check that both ARB files have the same keys

### Debugging

```dart
// Check current locale
print('Current locale: ${Localizations.localeOf(context)}');

// Check supported locales
print('Supported locales: ${AppLocalizations.supportedLocales}');

// Check if locale is supported
final isSupported = AppLocalizations.supportedLocales
    .any((locale) => locale.languageCode == 'vi');
```

## Adding New Languages

1. Create new ARB file (e.g., `app_fr.arb` for French)
2. Add locale to `LocaleManager.supportedLocales`
3. Update `LocaleManager.getDisplayName()` method
4. Run `flutter gen-l10n`
5. Test the new language

## Example Implementation

See `lib/presentation/screens/localization_demo_screen.dart` for a complete example demonstrating all localization features.

This internationalization system provides a robust foundation for multi-language support while maintaining backward compatibility and ease of use.
