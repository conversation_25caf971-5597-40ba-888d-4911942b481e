import 'package:shared_preferences/shared_preferences.dart';
import '../../core/constants/app_constants.dart';
import '../../core/security/token_manager.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/auth_api_service.dart';
import '../models/login_request.dart';
import '../models/login_response.dart';

class AuthRepositoryImpl implements AuthRepository {
  final AuthApiService _apiService;
  final SharedPreferences _prefs;
  final TokenManager _tokenManager;

  AuthRepositoryImpl({
    required AuthApiService apiService,
    required SharedPreferences prefs,
    TokenManager? tokenManager,
  }) : _apiService = apiService,
       _prefs = prefs,
       _tokenManager = tokenManager ?? TokenManager();

  @override
  Future<LoginResponse> login(LoginRequest request) async {
    try {
      final response = await _apiService.login(request);

      if (response.success && response.accessToken != null) {
        // Lưu token với secure storage
        await _tokenManager.saveAccessToken(
          response.accessToken!,
          expiresIn: response.expiresIn,
        );

        // Lưu remember me preference
        await saveRememberMe(request.rememberMe);

        // Lưu username nếu remember me được chọn
        if (request.rememberMe) {
          await saveUsername(request.username);
        } else {
          await _prefs.remove('stored_username');
        }
      }

      return response;
    } catch (e) {
      return LoginResponse(
        success: false,
        error: 'Lỗi đăng nhập: $e',
      );
    }
  }

  @override
  Future<void> logout() async {
    try {
      // Call API logout
      await _apiService.logout();
    } catch (e) {
      // Log error but continue with local logout
      // print('API logout error: $e'); // Remove print in production
    } finally {
      // Always clear local data
      await _tokenManager.clearAllTokens();

      // Only clear username if remember me is false
      final rememberMe = await getRememberMe();
      if (!rememberMe) {
        await clearStoredCredentials();
      }
    }
  }

  @override
  Future<bool> isLoggedIn() async {
    // Check if we have a valid token using TokenManager
    return await _tokenManager.hasValidToken();
  }

  @override
  Future<String?> getStoredToken() async {
    return await _tokenManager.getAccessToken();
  }

  @override
  Future<void> saveToken(String token) async {
    // Extract expiry from JWT if possible
    final expiry = _tokenManager.getJWTExpiry(token);
    int? expiresIn;

    if (expiry != null) {
      final now = DateTime.now();
      expiresIn = expiry.difference(now).inSeconds;
    }

    await _tokenManager.saveAccessToken(token, expiresIn: expiresIn);
  }

  @override
  Future<void> clearToken() async {
    await _tokenManager.clearAccessToken();
  }

  @override
  Future<bool> getRememberMe() async {
    return _prefs.getBool(AppConstants.rememberPasswordKey) ?? false;
  }

  @override
  Future<void> saveRememberMe(bool rememberMe) async {
    await _prefs.setBool(AppConstants.rememberPasswordKey, rememberMe);
  }

  @override
  Future<String?> getStoredUsername() async {
    return _prefs.getString('stored_username');
  }

  @override
  Future<void> saveUsername(String username) async {
    await _prefs.setString('stored_username', username);
  }

  @override
  Future<void> clearStoredCredentials() async {
    await _prefs.remove('stored_username');
    await _prefs.remove(AppConstants.rememberPasswordKey);
  }
}
