class LoginResponse {
  final bool success;
  final String? accessToken;
  final int? expiresIn;
  final String? message;
  final UserData? userInfo;
  final String? error;
  final int? code;
  final int? requestDuration;
  final String? traceId;

  const LoginResponse({
    required this.success,
    this.accessToken,
    this.expiresIn,
    this.message,
    this.userInfo,
    this.error,
    this.code,
    this.requestDuration,
    this.traceId,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    // Handle successful response format
    if (json.containsKey('data') && json['code'] == 200) {
      final data = json['data'] as Map<String, dynamic>;
      return LoginResponse(
        success: true,
        accessToken: data['accessToken'] as String?,
        expiresIn: data['expiresIn'] as int?,
        message: json['message'] as String?,
        userInfo: data['userInfo'] != null
            ? UserData.fromJson(data['userInfo'] as Map<String, dynamic>)
            : null,
        code: json['code'] as int?,
        requestDuration: json['requestDuration'] as int?,
        traceId: json['traceId'] as String?,
      );
    }

    // Handle error response format
    return LoginResponse(
      success: false,
      message: json['message'] as String?,
      error: json['error'] as String? ?? json['message'] as String?,
      code: json['code'] as int?,
      traceId: json['traceId'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'accessToken': accessToken,
      'expiresIn': expiresIn,
      'message': message,
      'userInfo': userInfo?.toJson(),
      'error': error,
      'code': code,
      'requestDuration': requestDuration,
      'traceId': traceId,
    };
  }

  @override
  String toString() {
    return 'LoginResponse(success: $success, accessToken: ${accessToken != null ? '[HIDDEN]' : null}, message: $message, userInfo: $userInfo, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LoginResponse &&
        other.success == success &&
        other.accessToken == accessToken &&
        other.expiresIn == expiresIn &&
        other.message == message &&
        other.userInfo == userInfo &&
        other.error == error &&
        other.code == code &&
        other.requestDuration == requestDuration &&
        other.traceId == traceId;
  }

  @override
  int get hashCode {
    return success.hashCode ^
        accessToken.hashCode ^
        expiresIn.hashCode ^
        message.hashCode ^
        userInfo.hashCode ^
        error.hashCode ^
        code.hashCode ^
        requestDuration.hashCode ^
        traceId.hashCode;
  }

  LoginResponse copyWith({
    bool? success,
    String? accessToken,
    int? expiresIn,
    String? message,
    UserData? userInfo,
    String? error,
    int? code,
    int? requestDuration,
    String? traceId,
  }) {
    return LoginResponse(
      success: success ?? this.success,
      accessToken: accessToken ?? this.accessToken,
      expiresIn: expiresIn ?? this.expiresIn,
      message: message ?? this.message,
      userInfo: userInfo ?? this.userInfo,
      error: error ?? this.error,
      code: code ?? this.code,
      requestDuration: requestDuration ?? this.requestDuration,
      traceId: traceId ?? this.traceId,
    );
  }

  // Getter for backward compatibility
  String? get token => accessToken;
  UserData? get userData => userInfo;
}

class UserData {
  final int? id;
  final bool? isActive;
  final String? userName;
  final String? fullName;
  final String? maCanBo;

  const UserData({
    this.id,
    this.isActive,
    this.userName,
    this.fullName,
    this.maCanBo,
  });

  factory UserData.fromJson(Map<String, dynamic> json) {
    return UserData(
      id: json['id'] as int?,
      isActive: json['isActive'] as bool?,
      userName: json['userName'] as String?,
      fullName: json['fullName'] as String?,
      maCanBo: json['maCanBo'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'isActive': isActive,
      'userName': userName,
      'fullName': fullName,
      'maCanBo': maCanBo,
    };
  }

  @override
  String toString() {
    return 'UserData(id: $id, isActive: $isActive, userName: $userName, fullName: $fullName, maCanBo: $maCanBo)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserData &&
        other.id == id &&
        other.isActive == isActive &&
        other.userName == userName &&
        other.fullName == fullName &&
        other.maCanBo == maCanBo;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        isActive.hashCode ^
        userName.hashCode ^
        fullName.hashCode ^
        maCanBo.hashCode;
  }

  UserData copyWith({
    int? id,
    bool? isActive,
    String? userName,
    String? fullName,
    String? maCanBo,
  }) {
    return UserData(
      id: id ?? this.id,
      isActive: isActive ?? this.isActive,
      userName: userName ?? this.userName,
      fullName: fullName ?? this.fullName,
      maCanBo: maCanBo ?? this.maCanBo,
    );
  }

  // Getters for backward compatibility
  String? get username => userName;
  String? get email => null; // Not provided in new API
  List<String>? get roles => null; // Not provided in new API
}
