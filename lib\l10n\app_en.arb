{"@@locale": "en", "@@last_modified": "2025-01-20T00:00:00.000Z", "ok": "OK", "@ok": {"description": "OK button text"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "save": "Save", "@save": {"description": "Save button text"}, "delete": "Delete", "@delete": {"description": "Delete button text"}, "edit": "Edit", "@edit": {"description": "Edit button text"}, "add": "Add", "@add": {"description": "Add button text"}, "search": "Search", "@search": {"description": "Search button text"}, "loading": "Loading...", "@loading": {"description": "Loading indicator text"}, "retry": "Retry", "@retry": {"description": "Retry button text"}, "error": "Error", "@error": {"description": "Error message title"}, "success": "Success", "@success": {"description": "Success message title"}, "warning": "Warning", "@warning": {"description": "Warning message title"}, "info": "Information", "@info": {"description": "Information message title"}, "login": "<PERSON><PERSON>", "@login": {"description": "Login button text"}, "logout": "Logout", "@logout": {"description": "Logout button text"}, "register": "Register", "@register": {"description": "Register button text"}, "username": "Username", "@username": {"description": "Username field label"}, "password": "Password", "@password": {"description": "Password field label"}, "confirmPassword": "Confirm Password", "@confirmPassword": {"description": "Confirm password field label"}, "forgotPassword": "Forgot Password?", "@forgotPassword": {"description": "Forgot password link text"}, "rememberPassword": "Remember Password", "@rememberPassword": {"description": "Remember password checkbox text"}, "loginSuccess": "Login successful", "@loginSuccess": {"description": "Login success message"}, "loginFailed": "<PERSON><PERSON> failed", "@loginFailed": {"description": "<PERSON><PERSON> failed message"}, "fieldRequired": "This field is required", "@fieldRequired": {"description": "Required field validation message"}, "invalidEmail": "Invalid email", "@invalidEmail": {"description": "Invalid email validation message"}, "passwordTooShort": "Password must be at least 6 characters", "@passwordTooShort": {"description": "Password too short validation message"}, "passwordsNotMatch": "Passwords do not match", "@passwordsNotMatch": {"description": "Passwords not matching validation message"}, "usernameTooShort": "Username must be at least 3 characters", "@usernameTooShort": {"description": "Username too short validation message"}, "noInternetConnection": "No internet connection", "@noInternetConnection": {"description": "No internet connection error message"}, "serverError": "Server error", "@serverError": {"description": "Server error message"}, "requestTimeout": "Request timeout", "@requestTimeout": {"description": "Request timeout error message"}, "unknownError": "Unknown error", "@unknownError": {"description": "Unknown error message"}, "home": "Home", "@home": {"description": "Home navigation item"}, "profile": "Profile", "@profile": {"description": "Profile navigation item"}, "settings": "Settings", "@settings": {"description": "Settings navigation item"}, "about": "About", "@about": {"description": "About navigation item"}, "language": "Language", "@language": {"description": "Language setting label"}, "selectLanguage": "Select Language", "@selectLanguage": {"description": "Select language dialog title"}, "appName": "Unisoft", "@appName": {"description": "Application name"}, "loadingApp": "Loading application...", "@loadingApp": {"description": "Loading application message"}, "checkingAuth": "Checking authentication...", "@checkingAuth": {"description": "Checking authentication status message"}, "welcome": "Welcome", "@welcome": {"description": "Welcome message"}, "dashboard": "Dashboard", "@dashboard": {"description": "Dashboard screen title"}, "overview": "Overview", "@overview": {"description": "Overview section title"}, "quickActions": "Quick Actions", "@quickActions": {"description": "Quick actions section title"}}