import 'package:flutter/material.dart';

extension ContextExtensions on BuildContext {
  // Theme shortcuts
  ThemeData get theme => Theme.of(this);
  TextTheme get textTheme => Theme.of(this).textTheme;
  ColorScheme get colorScheme => Theme.of(this).colorScheme;
  
  // MediaQuery shortcuts
  MediaQueryData get mediaQuery => MediaQuery.of(this);
  Size get screenSize => MediaQuery.of(this).size;
  double get screenWidth => MediaQuery.of(this).size.width;
  double get screenHeight => MediaQuery.of(this).size.height;
  EdgeInsets get padding => MediaQuery.of(this).padding;
  EdgeInsets get viewInsets => MediaQuery.of(this).viewInsets;
  double get devicePixelRatio => MediaQuery.of(this).devicePixelRatio;
  
  // Responsive breakpoints
  bool get isMobile => screenWidth < 768;
  bool get isTablet => screenWidth >= 768 && screenWidth < 1024;
  bool get isDesktop => screenWidth >= 1024;
  
  // Orientation
  bool get isPortrait => MediaQuery.of(this).orientation == Orientation.portrait;
  bool get isLandscape => MediaQuery.of(this).orientation == Orientation.landscape;
  
  // Navigation shortcuts
  NavigatorState get navigator => Navigator.of(this);
  
  void pop<T>([T? result]) => Navigator.of(this).pop(result);
  
  Future<T?> push<T>(Route<T> route) => Navigator.of(this).push(route);
  
  Future<T?> pushNamed<T>(String routeName, {Object? arguments}) =>
      Navigator.of(this).pushNamed(routeName, arguments: arguments);
  
  Future<T?> pushReplacement<T, TO>(Route<T> newRoute, {TO? result}) =>
      Navigator.of(this).pushReplacement(newRoute, result: result);
  
  Future<T?> pushReplacementNamed<T, TO>(String routeName, {TO? result, Object? arguments}) =>
      Navigator.of(this).pushReplacementNamed(routeName, result: result, arguments: arguments);
  
  Future<T?> pushAndRemoveUntil<T>(Route<T> newRoute, bool Function(Route<dynamic>) predicate) =>
      Navigator.of(this).pushAndRemoveUntil(newRoute, predicate);
  
  Future<T?> pushNamedAndRemoveUntil<T>(String newRouteName, bool Function(Route<dynamic>) predicate, {Object? arguments}) =>
      Navigator.of(this).pushNamedAndRemoveUntil(newRouteName, predicate, arguments: arguments);
  
  // Scaffold shortcuts
  ScaffoldState get scaffold => Scaffold.of(this);
  
  void showSnackBar(SnackBar snackBar) => ScaffoldMessenger.of(this).showSnackBar(snackBar);
  
  void hideCurrentSnackBar() => ScaffoldMessenger.of(this).hideCurrentSnackBar();
  
  void removeCurrentSnackBar() => ScaffoldMessenger.of(this).removeCurrentSnackBar();
  
  // Focus shortcuts
  void unfocus() => FocusScope.of(this).unfocus();
  
  void requestFocus(FocusNode focusNode) => FocusScope.of(this).requestFocus(focusNode);
  
  // Show dialogs
  Future<T?> showAlertDialog<T>({
    required String title,
    required String content,
    String confirmText = 'OK',
    String? cancelText,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
  }) {
    return showDialog<T>(
      context: this,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          if (cancelText != null)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onCancel?.call();
              },
              child: Text(cancelText),
            ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              onConfirm?.call();
            },
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }
  
  Future<T?> showLoadingDialog<T>({String? message}) {
    return showDialog<T>(
      context: this,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(width: 16),
            Text(message ?? 'Đang tải...'),
          ],
        ),
      ),
    );
  }
  
  // Show bottom sheet
  Future<T?> showBottomSheet<T>(Widget child) {
    return showModalBottomSheet<T>(
      context: this,
      builder: (context) => child,
    );
  }
  
  // Responsive values
  T responsive<T>({
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    if (isDesktop && desktop != null) return desktop;
    if (isTablet && tablet != null) return tablet;
    return mobile;
  }
  
  // Padding helpers
  EdgeInsets get safeAreaPadding => MediaQuery.of(this).padding;
  double get statusBarHeight => MediaQuery.of(this).padding.top;
  double get bottomPadding => MediaQuery.of(this).padding.bottom;
}
