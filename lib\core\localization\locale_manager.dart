import 'dart:ui';
import 'package:shared_preferences/shared_preferences.dart';

class LocaleManager {
  static const String _localeKey = 'selected_locale';
  static const List<Locale> supportedLocales = [
    Locale('en', 'US'), // English
    Locale('vi', 'VN'), // Vietnamese
  ];

  static Locale? _currentLocale;

  /// Get the current locale
  static Locale? get currentLocale => _currentLocale;

  /// Initialize the locale manager and load saved locale
  static Future<void> init() async {
    final prefs = await SharedPreferences.getInstance();
    final savedLocaleCode = prefs.getString(_localeKey);
    
    if (savedLocaleCode != null) {
      _currentLocale = _parseLocale(savedLocaleCode);
    } else {
      // Use device locale if supported, otherwise default to English
      final deviceLocale = PlatformDispatcher.instance.locale;
      _currentLocale = _isLocaleSupported(deviceLocale) 
          ? deviceLocale 
          : const Locale('en', 'US');
    }
  }

  /// Change the current locale and save it to preferences
  static Future<void> setLocale(Locale locale) async {
    if (!_isLocaleSupported(locale)) {
      throw ArgumentError('Locale $locale is not supported');
    }

    _currentLocale = locale;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_localeKey, locale.toString());
  }

  /// Get the locale for a given language code
  static Locale? getLocaleFromLanguageCode(String languageCode) {
    switch (languageCode.toLowerCase()) {
      case 'en':
        return const Locale('en', 'US');
      case 'vi':
        return const Locale('vi', 'VN');
      default:
        return null;
    }
  }

  /// Check if a locale is supported
  static bool _isLocaleSupported(Locale locale) {
    return supportedLocales.any((supportedLocale) =>
        supportedLocale.languageCode == locale.languageCode);
  }

  /// Parse locale from string format (e.g., "en_US")
  static Locale _parseLocale(String localeString) {
    final parts = localeString.split('_');
    if (parts.length == 2) {
      return Locale(parts[0], parts[1]);
    } else {
      return Locale(parts[0]);
    }
  }

  /// Get display name for a locale
  static String getDisplayName(Locale locale) {
    switch (locale.languageCode) {
      case 'en':
        return 'English';
      case 'vi':
        return 'Tiếng Việt';
      default:
        return locale.languageCode.toUpperCase();
    }
  }
}
