import 'package:flutter/material.dart';
import '../../core/constants/app_strings.dart';
import '../../core/extensions/localization_extensions.dart';
import '../../generated/l10n/app_localizations.dart';
import '../widgets/language_selector.dart';

/// Demo screen showcasing all localization features
class LocalizationDemoScreen extends StatefulWidget {
  const LocalizationDemoScreen({super.key});

  @override
  State<LocalizationDemoScreen> createState() => _LocalizationDemoScreenState();
}

class _LocalizationDemoScreenState extends State<LocalizationDemoScreen> {

  @override
  Widget build(BuildContext context) {
    // Initialize AppStrings for backward compatibility demo
    AppStrings.init(context);
    
    return Scaffold(
      appBar: AppBar(
        title: Text(context.l10n.about),
        actions: [
          IconButton(
            icon: const Icon(Icons.language),
            onPressed: () => _showLanguageSelector(),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSection(
              title: 'Basic Localization',
              children: [
                _buildExample(
                  'Using context.l10n (Recommended)',
                  context.l10n.login,
                ),
                _buildExample(
                  'Using AppLocalizations.of(context)',
                  AppLocalizations.of(context).register,
                ),
                _buildExample(
                  'Using AppStrings (Legacy)',
                  AppStrings.save,
                ),
              ],
            ),
            const SizedBox(height: 24),
            _buildSection(
              title: 'Common Strings',
              children: [
                _buildStringGrid([
                  context.l10n.ok,
                  context.l10n.cancel,
                  context.l10n.save,
                  context.l10n.delete,
                  context.l10n.edit,
                  context.l10n.add,
                  context.l10n.search,
                  context.l10n.loading,
                ]),
              ],
            ),
            const SizedBox(height: 24),
            _buildSection(
              title: 'Authentication Strings',
              children: [
                _buildStringGrid([
                  context.l10n.login,
                  context.l10n.logout,
                  context.l10n.register,
                  context.l10n.username,
                  context.l10n.password,
                  context.l10n.forgotPassword,
                ]),
              ],
            ),
            const SizedBox(height: 24),
            _buildSection(
              title: 'Validation Messages',
              children: [
                _buildStringList([
                  context.l10n.fieldRequired,
                  context.l10n.invalidEmail,
                  context.l10n.passwordTooShort,
                  context.l10n.passwordsNotMatch,
                  context.l10n.usernameTooShort,
                ]),
              ],
            ),
            const SizedBox(height: 24),
            _buildSection(
              title: 'Language Information',
              children: [
                _buildInfoCard([
                  'Current Language: ${context.languageCode.toUpperCase()}',
                  'Is Vietnamese: ${context.isVietnamese}',
                  'Is English: ${context.isEnglish}',
                  'Is RTL: ${context.isRTL}',
                  'Locale: ${context.locale}',
                ]),
              ],
            ),
            const SizedBox(height: 24),
            _buildSection(
              title: 'Language Selector Components',
              children: [
                const LanguageSelectorTile(),
                const SizedBox(height: 16),
                const LanguageDropdown(),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _showLanguageSelector,
                  child: Text(context.l10n.selectLanguage),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: Theme.of(context).primaryColor,
          ),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildExample(String description, String value) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              description,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStringGrid(List<String> strings) {
    return Wrap(
      spacing: 8.0,
      runSpacing: 8.0,
      children: strings.map((string) => Chip(
        label: Text(string),
        backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
      )).toList(),
    );
  }

  Widget _buildStringList(List<String> strings) {
    return Column(
      children: strings.map((string) => ListTile(
        leading: const Icon(Icons.error_outline, color: Colors.orange),
        title: Text(string),
        dense: true,
      )).toList(),
    );
  }

  Widget _buildInfoCard(List<String> info) {
    return Card(
      color: Theme.of(context).primaryColor.withValues(alpha: 0.05),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: info.map((item) => Padding(
            padding: const EdgeInsets.symmetric(vertical: 2.0),
            child: Row(
              children: [
                const Icon(Icons.info_outline, size: 16),
                const SizedBox(width: 8),
                Text(item),
              ],
            ),
          )).toList(),
        ),
      ),
    );
  }

  void _showLanguageSelector() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              context.l10n.selectLanguage,
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            const LanguageSelector(),
          ],
        ),
      ),
    );
  }
}
