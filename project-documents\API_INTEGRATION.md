# API Integration - Unisoft Flutter Application

## Tổng Quan

Ứng dụng đã được tích hợp với API đăng nhập sử dụng endpoint:
```
POST http://123.30.240.49:30200/auth/v1/authentication/jwt/login
```

## Cấu Trúc API Integration

### 1. **Models** (`lib/data/models/`)
- **LoginRequest**: Model cho request đăng nhập
- **LoginResponse**: Model cho response từ API
- **UserData**: Model cho thông tin user

### 2. **Network Layer** (`lib/core/network/`)
- **ApiClient**: HTTP client với error handling
- **Custom Exceptions**: ApiException, NetworkException

### 3. **Data Sources** (`lib/data/datasources/`)
- **AuthApiService**: Interface và implementation cho API calls
- **AuthApiServiceImpl**: Concrete implementation

### 4. **Repository Pattern** (`lib/data/repositories/`, `lib/domain/repositories/`)
- **AuthRepository**: Interface cho authentication operations
- **AuthRepositoryImpl**: Implementation với local storage

### 5. **Use Cases** (`lib/domain/usecases/`)
- **LoginUseCase**: Business logic cho đăng nhập

### 6. **Dependency Injection** (`lib/core/di/`)
- **ServiceLocator**: Quản lý dependencies

## API Request Format

```json
{
  "username": "admin",
  "password": "Abc@123",
  "rememberMe": true
}
```

## API Response Handling

Ứng dụng xử lý các trường hợp response sau:

### Success Response (200)
- Token được lưu vào SharedPreferences
- User được navigate đến Home screen
- Remember Me preference được lưu

### Error Response (4xx, 5xx)
- Hiển thị error message cho user
- Không lưu credentials

### Network Error
- Hiển thị "Không có kết nối internet"
- Retry mechanism

## Features Đã Triển Khai

### 1. **Authentication Flow**
- ✅ Splash screen với auto-login check
- ✅ Login screen với form validation
- ✅ API integration với error handling
- ✅ Token storage và management
- ✅ Remember Me functionality
- ✅ Auto-logout khi token invalid

### 2. **UI/UX Features**
- ✅ Loading states với CircularProgressIndicator
- ✅ Form validation với error messages
- ✅ Success/Error snackbars
- ✅ Password visibility toggle
- ✅ Remember password checkbox
- ✅ Responsive design

### 3. **Error Handling**
- ✅ Network connectivity errors
- ✅ API server errors
- ✅ Invalid credentials
- ✅ Timeout handling
- ✅ JSON parsing errors

### 4. **Local Storage**
- ✅ Token persistence
- ✅ Remember Me preference
- ✅ Username storage (khi Remember Me = true)
- ✅ Auto-clear credentials khi logout

## Cách Sử Dụng

### 1. **Đăng Nhập**
1. Mở ứng dụng
2. Nhập username và password
3. Chọn "Lưu mật khẩu" nếu muốn
4. Nhấn "Đăng nhập"
5. Chờ API response và navigate đến Home

### 2. **Remember Me**
- Khi được chọn: Username sẽ được lưu và tự động fill lần sau
- Token sẽ được validate khi mở app
- Auto-login nếu token còn valid

### 3. **Đăng Xuất**
- Nhấn icon logout ở Home screen
- Token và session data sẽ được clear
- Navigate về Login screen

## Testing

### Test Credentials
```
Username: admin
Password: Abc@123
```

### Test Cases
1. **Valid Login**: Sử dụng credentials trên
2. **Invalid Credentials**: Sử dụng sai username/password
3. **Network Error**: Tắt internet và thử đăng nhập
4. **Remember Me**: Test lưu và load credentials
5. **Auto-login**: Đóng app và mở lại

## Configuration

### API Endpoint
Có thể thay đổi trong `lib/core/network/api_client.dart`:
```dart
static const String baseUrl = 'http://123.30.240.49:30200';
```

### Timeouts
Có thể thay đổi trong `lib/core/constants/app_constants.dart`:
```dart
static const Duration connectionTimeout = Duration(seconds: 30);
static const Duration receiveTimeout = Duration(seconds: 30);
```

## Troubleshooting

### 1. **Connection Issues**
- Kiểm tra network connectivity
- Verify API endpoint accessibility
- Check firewall/proxy settings

### 2. **Authentication Failures**
- Verify credentials
- Check API server status
- Review error messages

### 3. **Token Issues**
- Clear app data để reset stored tokens
- Check token validation endpoint
- Verify token format

## Future Enhancements

### Planned Features
- [ ] Refresh token mechanism
- [ ] Biometric authentication
- [ ] Multi-factor authentication
- [ ] Social login integration
- [ ] Offline mode support

### API Improvements
- [ ] Logout endpoint integration
- [ ] Token refresh endpoint
- [ ] User profile endpoints
- [ ] Password reset functionality

## Dependencies

```yaml
dependencies:
  http: ^1.4.0
  shared_preferences: ^2.5.3
```

## Architecture Benefits

1. **Clean Architecture**: Tách biệt rõ ràng các layers
2. **Testability**: Dễ viết unit tests cho từng component
3. **Maintainability**: Code dễ đọc và bảo trì
4. **Scalability**: Dễ thêm features mới
5. **Error Handling**: Comprehensive error management

---

*Cập nhật lần cuối: 2025-07-20*
