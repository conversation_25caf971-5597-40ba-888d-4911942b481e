# Unisoft Flutter Application

A Flutter application with secure authentication and clean architecture.

## Features

- 🔐 Secure login with JWT token management
- 🛡️ Multi-layer token encryption and secure storage
- 🏗️ Clean Architecture with separation of concerns
- 📱 Cross-platform support (Android, iOS, Web, Desktop)
- 🎨 Material Design 3 UI
- 🔄 Automatic token refresh and validation
- 💾 Remember Me functionality
- 🌐 HTTP interceptor with auto-authentication

## Quick Start

### Prerequisites
- Flutter SDK (>=3.8.1)
- Dart SDK
- Android Studio / VS Code
- Device or emulator

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd unisoft_flutter_application
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Run the application**
   ```bash
   flutter run
   ```

### Test Credentials
- **Username**: `admin`
- **Password**: `Abc@123`

## Project Structure

```
lib/
├── main.dart                  # Entry point
├── app/                       # App configuration
├── core/                      # Core functionality
│   ├── constants/            # App constants
│   ├── network/              # HTTP clients & interceptors
│   ├── security/             # Token management & encryption
│   ├── utils/                # Utility functions
│   └── extensions/           # Dart extensions
├── data/                      # Data layer
├── domain/                    # Business logic layer
├── presentation/              # UI layer
└── shared/                    # Shared components
```

## Documentation

Comprehensive documentation is available in the `project-documents/` folder:

- **[Project Structure](project-documents/project_structure.md)** - Detailed project architecture
- **[API Integration](project-documents/API_INTEGRATION.md)** - API integration guide
- **[Token Security](project-documents/TOKEN_SECURITY.md)** - Security implementation details

## Architecture

This project follows **Clean Architecture** principles:

- **Presentation Layer**: UI components and state management
- **Domain Layer**: Business logic and use cases
- **Data Layer**: API calls, local storage, and repositories
- **Core Layer**: Shared utilities and configurations

## Security Features

- ✅ **Secure Token Storage**: Platform-specific secure storage
- ✅ **Token Encryption**: Additional XOR encryption layer
- ✅ **JWT Validation**: Local and server-side validation
- ✅ **Auto Token Management**: Automatic expiry handling
- ✅ **HTTP Interceptor**: Auto-attach tokens to requests
- ✅ **Session Management**: Secure login/logout flow

## API Endpoint

```
POST http://*************:30200/auth/v1/authentication/jwt/login
```

## Dependencies

Key dependencies used in this project:

```yaml
dependencies:
  flutter: sdk
  http: ^1.4.0
  shared_preferences: ^2.5.3
  flutter_secure_storage: ^9.2.4
  crypto: ^3.0.6
```

## Development

### Code Style
- Follow Dart/Flutter style guidelines
- Use meaningful variable and function names
- Add comments for complex logic
- Maintain consistent file structure

### Testing
```bash
# Run tests
flutter test

# Run with coverage
flutter test --coverage
```

### Build
```bash
# Build for Android
flutter build apk

# Build for iOS
flutter build ios

# Build for Web
flutter build web
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please refer to the documentation in the `project-documents/` folder or contact the development team.

---

**Version**: 1.0.0  
**Last Updated**: 2025-07-20
