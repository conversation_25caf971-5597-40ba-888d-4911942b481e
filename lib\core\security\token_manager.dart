import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../constants/app_constants.dart';

class TokenManager {
  static const String _tokenKey = 'secure_access_token';
  static const String _tokenExpiryKey = 'token_expiry';
  static const String _refreshTokenKey = 'secure_refresh_token';
  static const String _encryptionKeyKey = 'encryption_key';
  
  final FlutterSecureStorage _secureStorage;
  
  TokenManager({FlutterSecureStorage? secureStorage})
      : _secureStorage = secureStorage ?? const FlutterSecureStorage(
          aOptions: AndroidOptions(
            encryptedSharedPreferences: true,
            sharedPreferencesName: 'unisoft_secure_prefs',
            preferencesKeyPrefix: 'unisoft_',
          ),
          iOptions: IOSOptions(
            groupId: 'group.com.unisoft.app',
            accountName: 'unisoft_account',
            accessibility: IOSAccessibility.first_unlock_this_device,
          ),
          lOptions: LinuxOptions(
            containsKey: true,
          ),
          wOptions: WindowsOptions(
            containsKey: true,
          ),
        );

  // Save access token with encryption
  Future<void> saveAccessToken(String token, {int? expiresIn}) async {
    try {
      final encryptedToken = await _encryptData(token);
      await _secureStorage.write(key: _tokenKey, value: encryptedToken);
      
      // Save expiry time if provided
      if (expiresIn != null) {
        final expiryTime = DateTime.now().add(Duration(seconds: expiresIn));
        await _secureStorage.write(
          key: _tokenExpiryKey, 
          value: expiryTime.millisecondsSinceEpoch.toString(),
        );
      }
    } catch (e) {
      throw TokenStorageException('Failed to save access token: $e');
    }
  }

  // Get access token with decryption
  Future<String?> getAccessToken() async {
    try {
      final encryptedToken = await _secureStorage.read(key: _tokenKey);
      if (encryptedToken == null) return null;
      
      return await _decryptData(encryptedToken);
    } catch (e) {
      // If decryption fails, clear the corrupted token
      await clearAccessToken();
      return null;
    }
  }

  // Check if token is expired
  Future<bool> isTokenExpired() async {
    try {
      final expiryString = await _secureStorage.read(key: _tokenExpiryKey);
      if (expiryString == null) return true;
      
      final expiryTime = DateTime.fromMillisecondsSinceEpoch(
        int.parse(expiryString),
      );
      
      // Add 5 minute buffer before actual expiry
      final bufferTime = expiryTime.subtract(const Duration(minutes: 5));
      return DateTime.now().isAfter(bufferTime);
    } catch (e) {
      return true; // Assume expired if we can't determine
    }
  }

  // Get token expiry time
  Future<DateTime?> getTokenExpiry() async {
    try {
      final expiryString = await _secureStorage.read(key: _tokenExpiryKey);
      if (expiryString == null) return null;
      
      return DateTime.fromMillisecondsSinceEpoch(int.parse(expiryString));
    } catch (e) {
      return null;
    }
  }

  // Save refresh token (if your API supports it)
  Future<void> saveRefreshToken(String refreshToken) async {
    try {
      final encryptedToken = await _encryptData(refreshToken);
      await _secureStorage.write(key: _refreshTokenKey, value: encryptedToken);
    } catch (e) {
      throw TokenStorageException('Failed to save refresh token: $e');
    }
  }

  // Get refresh token
  Future<String?> getRefreshToken() async {
    try {
      final encryptedToken = await _secureStorage.read(key: _refreshTokenKey);
      if (encryptedToken == null) return null;
      
      return await _decryptData(encryptedToken);
    } catch (e) {
      await clearRefreshToken();
      return null;
    }
  }

  // Clear access token
  Future<void> clearAccessToken() async {
    await _secureStorage.delete(key: _tokenKey);
    await _secureStorage.delete(key: _tokenExpiryKey);
  }

  // Clear refresh token
  Future<void> clearRefreshToken() async {
    await _secureStorage.delete(key: _refreshTokenKey);
  }

  // Clear all tokens
  Future<void> clearAllTokens() async {
    await Future.wait([
      clearAccessToken(),
      clearRefreshToken(),
    ]);
  }

  // Check if user has valid token
  Future<bool> hasValidToken() async {
    final token = await getAccessToken();
    if (token == null || token.isEmpty) return false;
    
    final isExpired = await isTokenExpired();
    return !isExpired;
  }

  // Encrypt data using AES
  Future<String> _encryptData(String data) async {
    try {
      final key = await _getOrCreateEncryptionKey();
      final bytes = utf8.encode(data);
      final keyBytes = utf8.encode(key);
      
      // Simple XOR encryption (for demo - use proper AES in production)
      final encrypted = <int>[];
      for (int i = 0; i < bytes.length; i++) {
        encrypted.add(bytes[i] ^ keyBytes[i % keyBytes.length]);
      }
      
      return base64.encode(encrypted);
    } catch (e) {
      throw TokenStorageException('Failed to encrypt data: $e');
    }
  }

  // Decrypt data
  Future<String> _decryptData(String encryptedData) async {
    try {
      final key = await _getOrCreateEncryptionKey();
      final encrypted = base64.decode(encryptedData);
      final keyBytes = utf8.encode(key);
      
      // Simple XOR decryption
      final decrypted = <int>[];
      for (int i = 0; i < encrypted.length; i++) {
        decrypted.add(encrypted[i] ^ keyBytes[i % keyBytes.length]);
      }
      
      return utf8.decode(decrypted);
    } catch (e) {
      throw TokenStorageException('Failed to decrypt data: $e');
    }
  }

  // Get or create encryption key
  Future<String> _getOrCreateEncryptionKey() async {
    String? key = await _secureStorage.read(key: _encryptionKeyKey);
    
    if (key == null) {
      // Generate new encryption key
      key = _generateRandomKey(32);
      await _secureStorage.write(key: _encryptionKeyKey, value: key);
    }
    
    return key;
  }

  // Generate random encryption key
  String _generateRandomKey(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random.secure();
    return String.fromCharCodes(
      Iterable.generate(
        length,
        (_) => chars.codeUnitAt(random.nextInt(chars.length)),
      ),
    );
  }

  // Validate token format (JWT)
  bool isValidJWTFormat(String token) {
    final parts = token.split('.');
    return parts.length == 3;
  }

  // Extract payload from JWT (without verification)
  Map<String, dynamic>? extractJWTPayload(String token) {
    try {
      if (!isValidJWTFormat(token)) return null;
      
      final parts = token.split('.');
      final payload = parts[1];
      
      // Add padding if needed
      String normalizedPayload = payload;
      while (normalizedPayload.length % 4 != 0) {
        normalizedPayload += '=';
      }
      
      final decoded = base64.decode(normalizedPayload);
      final jsonString = utf8.decode(decoded);
      return json.decode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  // Get token expiry from JWT
  DateTime? getJWTExpiry(String token) {
    try {
      final payload = extractJWTPayload(token);
      if (payload == null) return null;
      
      final exp = payload['exp'] as int?;
      if (exp == null) return null;
      
      return DateTime.fromMillisecondsSinceEpoch(exp * 1000);
    } catch (e) {
      return null;
    }
  }

  // Dispose resources
  void dispose() {
    // FlutterSecureStorage doesn't need explicit disposal
  }
}

// Custom exception for token storage errors
class TokenStorageException implements Exception {
  final String message;
  
  const TokenStorageException(this.message);
  
  @override
  String toString() => 'TokenStorageException: $message';
}
